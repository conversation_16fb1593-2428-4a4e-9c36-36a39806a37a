# Basic Android ProGuard rules
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep llama.cpp JNI interface
-keep class android.llama.cpp.** { *; }

# Disable obfuscation for library
-dontobfuscate

# Keep model classes from obfuscation
-keep class * extends android.llama.cpp.LLamaAndroid { *; }

# Keep public APIs
-keep public class * {
    public protected *;
}

# Obfuscate everything else
-keepattributes Signature,InnerClasses,EnclosingMethod
-keepattributes RuntimeVisibleAnnotations,RuntimeVisibleParameterAnnotations

# Advanced obfuscation options
-overloadaggressively
-repackageclasses ''
-allowaccessmodification

-dontwarn java.lang.invoke.StringConcatFactory


# Kompute Game Engine Integration (Godot)

![](https://github.com/KomputeProject/kompute/raw/master/docs/images/komputer-godot-4.gif)

This repository contains the accompanying code for the Blog post ["Supercharging Game Development with GPU Accelerated Machine Learning"](https://medium.com/@AxSaucedo/supercharging-game-development-with-gpu-accelerated-ml-using-vulkan-kompute-the-godot-game-engine-4e75a84ea9f0).

This example folder contains three key components:
* The Godot Project file `project.godot` to run the example
* The [instructions for the Custom Module](./custom_module/) implementation
* The [instructions for the GdNative Library](./gdnative_shared/) implementation

You can also find the simpler Godot Summator example implemented in the GPU in [this example folder](../godot_examples/).


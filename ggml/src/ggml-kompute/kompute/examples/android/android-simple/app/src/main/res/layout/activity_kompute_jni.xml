<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_kompute_jni"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.ethicalml.kompute.KomputeJni">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="1dp">

        <Space
            android:layout_width="match_parent"
            android:layout_height="10dp" />

        <LinearLayout
            android:layout_width="409dp"
            android:layout_height="50dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/imageView"
                android:layout_width="10dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:adjustViewBounds="true"
                android:cropToPadding="true"
                android:src="@mipmap/kompute_icon_foreground" />

            <TextView
                android:id="@+id/textView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Kompute Android"
                android:textSize="24sp"
                android:textStyle="bold" />
        </LinearLayout>

        <Space
            android:layout_width="match_parent"
            android:layout_height="10dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Input Xi"
                android:textSize="24sp" />

            <EditText
                android:id="@+id/XIEditText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:ems="10"
                android:inputType="textPersonName"
                android:text="[ 0, 1, 1, 1, 1, 1 ]" />
        </LinearLayout>

        <Space
            android:layout_width="match_parent"
            android:layout_height="10dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Input Xj"
                android:textSize="24sp" />

            <EditText
                android:id="@+id/XJEditText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:ems="10"
                android:inputType="textPersonName"
                android:text="[ 0, 0, 0, 1, 1, 1 ]" />

        </LinearLayout>

        <Space
            android:layout_width="match_parent"
            android:layout_height="10dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Input Y"
                android:textSize="24sp" />

            <EditText
                android:id="@+id/YEditText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:ems="10"
                android:inputType="textPersonName"
                android:text="[ 0, 0, 0, 1, 1, 1 ]" />

        </LinearLayout>

        <Space
            android:layout_width="match_parent"
            android:layout_height="10dp" />

        <Button
            android:id="@+id/KomputeButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:onClick="KomputeButtonOnClick"
            android:text="Kompute" />

        <Space
            android:layout_width="match_parent"
            android:layout_height="10dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="W 1: "
                android:textSize="14sp" />

            <TextView
                android:id="@+id/wOneTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="N/A"
                android:textSize="14sp"
                android:textStyle="bold" />

            <Space
                android:layout_width="10dp"
                android:layout_height="match_parent" />

            <TextView
                android:id="@+id/textView3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="W 2: "
                android:textSize="14sp" />

            <TextView
                android:id="@+id/wTwoTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="N/A"
                android:textSize="14sp"
                android:textStyle="bold" />

            <Space
                android:layout_width="10dp"
                android:layout_height="match_parent"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/textView5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="B : "
                android:textSize="14sp" />

            <TextView
                android:id="@+id/biasTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="N/A"
                android:textSize="14sp"
                android:textStyle="bold" />
        </LinearLayout>

        <Space
            android:layout_width="match_parent"
            android:layout_height="10dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Result:"
                android:textSize="24sp" />

            <TextView
                android:id="@+id/predictionTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="TextView"
                android:textStyle="bold" />

        </LinearLayout>

        <Space
            android:layout_width="match_parent"
            android:layout_height="20dp" />

        <WebView
            android:id="@+id/kompute_gif_view"
            android:layout_width="409dp"
            android:layout_height="230dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>


# Kompute Mobile App Integration (Android)

This is the accompanying code for the Blog post ["Supercharging your Mobile Apps with On-Device GPU Accelerated Machine Learning using the Android NDK & Kompute"](https://towardsdatascience.com/gpu-accelerated-machine-learning-in-your-mobile-applications-using-the-android-ndk-vulkan-kompute-1e9da37b7617). 

<table>
<tr>

<td width="70%">
<h3>Example Running Logistic Regression with Kompute in Android Phones</h3>

<p>
This example provides an end to end example that can be run using android studio.

The example uses the Kompute built-in the relative folder to build the respective binaries for android.

The build structure provides a range of options to built-in different Android hardware. This example was tested in various emulators including Pixel 2, and a physical Samsung S7 phone.
</p>

<br>

<img src="https://raw.githubusercontent.com/KomputeProject/kompute/android-example/docs/images/android-editor.jpg">

</td>


<td width="30%">
<img src="https://raw.githubusercontent.com/KomputeProject/kompute/android-example/docs/images/android-kompute.jpg">
</td>

</tr>
</table>






%clang

-fdeclspec
-fms-extensions
-Wall
-Wextra
-std=c++17

%h -x
%h c++-header

-DDEBUG=1
-DKOMPUTE_INCLUDE_FOR_SYNTAX

-I/usr/include/python3.6/
-I./python/pybind11/include/

-I./build/_deps/vulkan_header-src/include/
-I./build/_deps/spdlog-src/include/
-I./build/_deps/googletest-src/googletest/include/
-I./build/_deps/fmt-src/include/

-I./src/include/
-I./build/src/shaders/glsl/
-I./build/test/shaders/glsl/
-I./test/utils/

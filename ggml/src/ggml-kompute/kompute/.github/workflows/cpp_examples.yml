name: C++ Tests

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  array-multiplication-example:
    runs-on: ubuntu-latest
    container: axsauze/kompute-builder:0.4
    env:
      VK_ICD_FILENAMES: "/swiftshader/vk_swiftshader_icd.json"
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        submodules: false
    - name: "[Release g++] Build & Test"
      uses: KomputeProject/action-cmake-build@master
      with:
        build-dir: ${{github.workspace}}/examples/array_multiplication/build
        source-dir: ${{github.workspace}}/examples/array_multiplication
        cc: gcc
        cxx: g++
        build-type: Debug
        run-test: false
        ctest-options: -V
        configure-options: -DKOMPUTE_OPT_USE_BUILT_IN_VULKAN_HEADER=ON KOMPUTE_OPT_FROM_SOURCE=ON
        build-options: --parallel # Given we don't build too many resources we can leverage parallel
    - name: Run tests
      run: ./examples/array_multiplication/build/src/kompute_array_mult

  logistc-regression-example:
    runs-on: ubuntu-latest
    container: axsauze/kompute-builder:0.4
    env:
      VK_ICD_FILENAMES: "/swiftshader/vk_swiftshader_icd.json"
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        submodules: false
    - name: "[Release g++] Build & Test"
      uses: KomputeProject/action-cmake-build@master
      with:
        build-dir: ${{github.workspace}}/examples/logistic_regression/build
        source-dir: ${{github.workspace}}/examples/logistic_regression
        cc: gcc
        cxx: g++
        build-type: Debug
        run-test: false
        ctest-options: -V
        configure-options: -DKOMPUTE_OPT_USE_BUILT_IN_VULKAN_HEADER=ON KOMPUTE_OPT_FROM_SOURCE=ON
        build-options: --parallel # Given we don't build too many resources we can leverage parallel
    - name: Run tests
      run: ./examples/logistic_regression/build/src/kompute_logistic_regression

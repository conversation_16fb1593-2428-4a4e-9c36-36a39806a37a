name: Python Tests

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  python-tests:
    runs-on: ubuntu-latest
    container: axsauze/kompute-builder:0.4
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        submodules: false
    - name: Install Python Requirements
      run: pip3 install --user -r python/test/requirements-dev.txt
    - name: Python Build
      env:
        KOMPUTE_PYTHON_NUM_PARALLEL_THREADS: 2
        KOMPUTE_OPT_USE_BUILT_IN_VULKAN_HEADER: ON
      run: pip3 install --user . -v
    - name: Python run Tests
      run: |
        export VK_ICD_FILENAMES=/swiftshader/vk_swiftshader_icd.json
        make test_python

name: C++ Tests

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  cpp-tests-debug-with-debug-layers:
    runs-on: ubuntu-latest
    container: axsauze/kompute-builder:0.4
    env:
      VK_ICD_FILENAMES: "/swiftshader/vk_swiftshader_icd.json"
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        submodules: false
    - name: "[Release g++] Build & Test"
      uses: KomputeProject/action-cmake-build@master
      with:
        build-dir: ${{github.workspace}}/build
        source-dir: ${{github.workspace}}
        cc: gcc
        cxx: g++
        build-type: Debug
        run-test: false
        ctest-options: -V
        configure-options: -DKOMPUTE_OPT_BUILD_TESTS=ON -DKOMPUTE_OPT_DISABLE_VK_DEBUG_LAYERS=OFF -DKOMPUTE_OPT_USE_BUILT_IN_VULKAN_HEADER=ON
    - name: Run tests
      run: make mk_run_tests

  cpp-tests-release-with-debug-layers:
    runs-on: ubuntu-latest
    container: axsauze/kompute-builder:0.4
    env:
      VK_ICD_FILENAMES: "/swiftshader/vk_swiftshader_icd.json"
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        submodules: false
    - name: "[Release g++] Build & Test"
      uses: KomputeProject/action-cmake-build@master
      with:
        build-dir: ${{github.workspace}}/build
        source-dir: ${{github.workspace}}
        cc: gcc
        cxx: g++
        build-type: Release
        run-test: false
        ctest-options: -V
        configure-options: -DKOMPUTE_OPT_BUILD_TESTS=ON -DKOMPUTE_OPT_DISABLE_VK_DEBUG_LAYERS=OFF -DKOMPUTE_OPT_USE_BUILT_IN_VULKAN_HEADER=ON
    - name: Run tests
      run: make mk_run_tests

  cpp-tests-debug-without-debug-layers:
    runs-on: ubuntu-latest
    container: axsauze/kompute-builder:0.4
    env:
      VK_ICD_FILENAMES: "/swiftshader/vk_swiftshader_icd.json"
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        submodules: false
    - name: "[Release g++] Build & Test"
      uses: KomputeProject/action-cmake-build@master
      with:
        build-dir: ${{github.workspace}}/build
        source-dir: ${{github.workspace}}
        cc: gcc
        cxx: g++
        build-type: Debug
        run-test: false
        ctest-options: -V
        configure-options: -DKOMPUTE_OPT_BUILD_TESTS=ON -DKOMPUTE_OPT_DISABLE_VK_DEBUG_LAYERS=ON -DKOMPUTE_OPT_USE_BUILT_IN_VULKAN_HEADER=ON
    - name: Run tests
      run: make mk_run_tests
  
  cpp-tests-release-without-debug-layers:
    runs-on: ubuntu-latest
    container: axsauze/kompute-builder:0.4
    env:
      VK_ICD_FILENAMES: "/swiftshader/vk_swiftshader_icd.json"
    steps:
    - name: Checkout
      uses: actions/checkout@v3
      with:
        submodules: false
    - name: "[Release g++] Build & Test"
      uses: KomputeProject/action-cmake-build@master
      with:
        build-dir: ${{github.workspace}}/build
        source-dir: ${{github.workspace}}
        cc: gcc
        cxx: g++
        build-type: Release
        run-test: false
        ctest-options: -V
        configure-options: -DKOMPUTE_OPT_BUILD_TESTS=ON -DKOMPUTE_OPT_DISABLE_VK_DEBUG_LAYERS=ON -DKOMPUTE_OPT_USE_BUILT_IN_VULKAN_HEADER=ON
    - name: Run tests
      run: make mk_run_tests

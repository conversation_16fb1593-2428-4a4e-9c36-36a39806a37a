#pragma once
#include <array>
#include <cstdint>

namespace kp {
const std::array<uint32_t, 366> SHADEROPMULT_COMP_SPV = { 
0x07230203, 0x00010000, 0x0008000a, 0x0000002e, 
0x00000000, 0x00020011, 0x00000001, 0x0006000b, 
0x00000001, 0x4c534c47, 0x6474732e, 0x3035342e, 
0x00000000, 0x0003000e, 0x00000000, 0x00000001, 
0x0006000f, 0x00000005, 0x00000004, 0x6e69616d, 
0x00000000, 0x0000000b, 0x00060010, 0x00000004, 
0x00000011, 0x00000001, 0x00000001, 0x00000001, 
0x00030003, 0x00000002, 0x000001c2, 0x00040005, 
0x00000004, 0x6e69616d, 0x00000000, 0x00040005, 
0x00000008, 0x65646e69, 0x00000078, 0x00080005, 
0x0000000b, 0x475f6c67, 0x61626f6c, 0x766e496c, 
0x7461636f, 0x496e6f69, 0x00000044, 0x00060005, 
0x00000012, 0x736e6574, 0x754f726f, 0x74757074, 
0x00000000, 0x00070006, 0x00000012, 0x00000000, 
0x756c6176, 0x754f7365, 0x74757074, 0x00000000, 
0x00030005, 0x00000014, 0x00000000, 0x00050005, 
0x00000019, 0x736e6574, 0x684c726f, 0x00000073, 
0x00060006, 0x00000019, 0x00000000, 0x756c6176, 
0x684c7365, 0x00000073, 0x00030005, 0x0000001b, 
0x00000000, 0x00050005, 0x00000021, 0x736e6574, 
0x6852726f, 0x00000073, 0x00060006, 0x00000021, 
0x00000000, 0x756c6176, 0x68527365, 0x00000073, 
0x00030005, 0x00000023, 0x00000000, 0x00040005, 
0x00000029, 0x5f4e454c, 0x0053484c, 0x00040005, 
0x0000002a, 0x5f4e454c, 0x00534852, 0x00040005, 
0x0000002b, 0x5f4e454c, 0x0054554f, 0x00040047, 
0x0000000b, 0x0000000b, 0x0000001c, 0x00040047, 
0x00000011, 0x00000006, 0x00000004, 0x00050048, 
0x00000012, 0x00000000, 0x00000023, 0x00000000, 
0x00030047, 0x00000012, 0x00000003, 0x00040047, 
0x00000014, 0x00000022, 0x00000000, 0x00040047, 
0x00000014, 0x00000021, 0x00000002, 0x00040047, 
0x00000018, 0x00000006, 0x00000004, 0x00050048, 
0x00000019, 0x00000000, 0x00000023, 0x00000000, 
0x00030047, 0x00000019, 0x00000003, 0x00040047, 
0x0000001b, 0x00000022, 0x00000000, 0x00040047, 
0x0000001b, 0x00000021, 0x00000000, 0x00040047, 
0x00000020, 0x00000006, 0x00000004, 0x00050048, 
0x00000021, 0x00000000, 0x00000023, 0x00000000, 
0x00030047, 0x00000021, 0x00000003, 0x00040047, 
0x00000023, 0x00000022, 0x00000000, 0x00040047, 
0x00000023, 0x00000021, 0x00000001, 0x00040047, 
0x00000029, 0x00000001, 0x00000000, 0x00040047, 
0x0000002a, 0x00000001, 0x00000001, 0x00040047, 
0x0000002b, 0x00000001, 0x00000002, 0x00040047, 
0x0000002d, 0x0000000b, 0x00000019, 0x00020013, 
0x00000002, 0x00030021, 0x00000003, 0x00000002, 
0x00040015, 0x00000006, 0x00000020, 0x00000000, 
0x00040020, 0x00000007, 0x00000007, 0x00000006, 
0x00040017, 0x00000009, 0x00000006, 0x00000003, 
0x00040020, 0x0000000a, 0x00000001, 0x00000009, 
0x0004003b, 0x0000000a, 0x0000000b, 0x00000001, 
0x0004002b, 0x00000006, 0x0000000c, 0x00000000, 
0x00040020, 0x0000000d, 0x00000001, 0x00000006, 
0x00030016, 0x00000010, 0x00000020, 0x0003001d, 
0x00000011, 0x00000010, 0x0003001e, 0x00000012, 
0x00000011, 0x00040020, 0x00000013, 0x00000002, 
0x00000012, 0x0004003b, 0x00000013, 0x00000014, 
0x00000002, 0x00040015, 0x00000015, 0x00000020, 
0x00000001, 0x0004002b, 0x00000015, 0x00000016, 
0x00000000, 0x0003001d, 0x00000018, 0x00000010, 
0x0003001e, 0x00000019, 0x00000018, 0x00040020, 
0x0000001a, 0x00000002, 0x00000019, 0x0004003b, 
0x0000001a, 0x0000001b, 0x00000002, 0x00040020, 
0x0000001d, 0x00000002, 0x00000010, 0x0003001d, 
0x00000020, 0x00000010, 0x0003001e, 0x00000021, 
0x00000020, 0x00040020, 0x00000022, 0x00000002, 
0x00000021, 0x0004003b, 0x00000022, 0x00000023, 
0x00000002, 0x00040032, 0x00000006, 0x00000029, 
0x00000000, 0x00040032, 0x00000006, 0x0000002a, 
0x00000000, 0x00040032, 0x00000006, 0x0000002b, 
0x00000000, 0x0004002b, 0x00000006, 0x0000002c, 
0x00000001, 0x0006002c, 0x00000009, 0x0000002d, 
0x0000002c, 0x0000002c, 0x0000002c, 0x00050036, 
0x00000002, 0x00000004, 0x00000000, 0x00000003, 
0x000200f8, 0x00000005, 0x0004003b, 0x00000007, 
0x00000008, 0x00000007, 0x00050041, 0x0000000d, 
0x0000000e, 0x0000000b, 0x0000000c, 0x0004003d, 
0x00000006, 0x0000000f, 0x0000000e, 0x0003003e, 
0x00000008, 0x0000000f, 0x0004003d, 0x00000006, 
0x00000017, 0x00000008, 0x0004003d, 0x00000006, 
0x0000001c, 0x00000008, 0x00060041, 0x0000001d, 
0x0000001e, 0x0000001b, 0x00000016, 0x0000001c, 
0x0004003d, 0x00000010, 0x0000001f, 0x0000001e, 
0x0004003d, 0x00000006, 0x00000024, 0x00000008, 
0x00060041, 0x0000001d, 0x00000025, 0x00000023, 
0x00000016, 0x00000024, 0x0004003d, 0x00000010, 
0x00000026, 0x00000025, 0x00050085, 0x00000010, 
0x00000027, 0x0000001f, 0x00000026, 0x00060041, 
0x0000001d, 0x00000028, 0x00000014, 0x00000016, 
0x00000017, 0x0003003e, 0x00000028, 0x00000027, 
0x000100fd, 0x00010038 };
} // namespace kp



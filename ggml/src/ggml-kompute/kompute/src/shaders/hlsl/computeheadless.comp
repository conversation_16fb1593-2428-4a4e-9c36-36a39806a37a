// Copyright 2020 Google LLC

RWStructuredBuffer<uint> values : register(u0);
[[vk::constant_id(0)]] const uint BUFFER_ELEMENTS = 32;

uint fibonacci(uint n) {
	if(n <= 1){
		return n;
	}
	uint curr = 1;
	uint prev = 1;
	for(uint i = 2; i < n; ++i) {
		uint temp = curr;
		curr += prev;
		prev = temp;
	}
	return curr;
}

[numthreads(1, 1, 1)]
void main(uint3 GlobalInvocationID : SV_DispatchThreadID)
{
	uint index = GlobalInvocationID.x;
	if (index >= BUFFER_ELEMENTS)
		return;
	values[index] = fibonacci(values[index]);
}



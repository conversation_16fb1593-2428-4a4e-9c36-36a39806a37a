
.. mdinclude:: ../../examples/python_naive_matmul/README.md


Implementation Overview
---------

The benchmark can be found in the `benchmark.py` file in the repo, which is outlined below. This file runs a naive implementation of the three matrix multiplication implementations to evaluate the performance of each.


.. literalinclude:: ../../examples/python_naive_matmul/benchmark.py
   :language: python



Kompute follows [Linux Foundation's Code of Conduct](https://lfprojects.org/policies/code-of-conduct/).

# Introduction 

The purposes of LF Projects, LLC (“LF Projects”) are to:

    support the collaborative development, availability and adoption of open source software, hardware and networking and other technologies and the collaborative development, availability and adoption of open protocols and standards (individually and collectively, “Open Technology”);
    host various projects pursuing the development of Open Technology and other technical assets, materials and processes (each such project, which itself may include any number of projects, a “Project”);
    provide enablement and support to Projects to assist their development activities; and
    undertake such other lawful activity as permitted by law and as consistent with the mission, purpose and tax status of LFP, Inc., a Delaware non-profit non-stock corporation and the sole member of LF Projects.

LF Projects hosts communities where participants choose to work together, and in that process experience differences in language, location, nationality, and experience. In such a diverse environment, misunderstandings and disagreements happen, which in most cases can be resolved informally. In rare cases, however, behavior can intimidate, harass, or otherwise disrupt one or more people in the community, which LF Projects will not tolerate.

A Code of Conduct (“Code”) is useful to define accepted and acceptable behaviors and to promote high standards of professional practice. It also provides a benchmark for self-evaluation and acts as a vehicle for better identity of the organization.

LF Projects is a Delaware series limited liability company.  Projects of LF Projects are formed as separate series of LF Projects (each, a “Series”).  References to “Projects” within this Policy include the applicable Series for each Project.

This Code applies to any participant of any Project – including without limitation developers, participants in meetings, teleconferences, mailing lists, conferences or functions, and contributors. Note that this Code complements rather than replaces legal rights and obligations pertaining to any particular situation.  In addition, with the approval of LF Projects, Projects are free to adopt their own code of conduct in place of the Code.

## Statement of Intent

LF Projects is committed to maintain a positive, professional work environment. This commitment calls for workplaces where participants at all levels behave according to the rules of the following code. A foundational concept of this code is that we all share responsibility for our work environment.

## Code

    Treat each other with respect, professionalism, fairness, and sensitivity to our many differences and strengths, including in situations of high pressure and urgency.
    Never harass or bully anyone verbally, physically or sexually.
    Never discriminate on the basis of personal characteristics or group membership.
    Communicate constructively and avoid demeaning or insulting behavior or language.
    Seek, accept, and offer objective work criticism, and acknowledge properly the contributions of others.
    Be honest about your own qualifications, and about any circumstances that might lead to conflicts of interest.
    Respect the privacy of others and the confidentiality of data you access.
    With respect to cultural differences, be conservative in what you do and liberal in what you accept from others, but not to the point of accepting disrespectful, unprofessional or unfair or unwelcome behavior or advances.
    Promote the rules of this Code and take action (especially if you are in a leadership position) to bring the discussion back to a more civil level whenever inappropriate behaviors are observed.
    Stay on topic: Make sure that you are posting to the correct channel and avoid off-topic discussions. Remember when you update an issue or respond to an email you are potentially sending to a large number of people.
    Step down considerately: participants in every project come and go, and LF Projects is no different. When you leave or disengage from the project, in whole or in part, we ask that you do so in a way that minimizes disruption to the project. This means you should tell people you are leaving and take the proper steps to ensure that others can pick up where you left off. 

# Glossary

## Demeaning behavior

is acting in a way that reduces another person’s dignity, sense of self-worth or respect within the community.

## Discrimination

is the prejudicial treatment of an individual based on criteria such as: physical appearance, race, ethnic origin, genetic differences, national or social origin, name, religion, gender, sexual orientation, family or health situation, pregnancy, disability, age, education, wealth, domicile, political view, morals, employment, or union activity.

## Insulting behavior

is treating another person with scorn or disrespect.

## Acknowledgement

is a record of the origin(s) and author(s) of a contribution.

## Harassment

is any conduct, verbal or physical, that has the intent or effect of interfering with an individual, or that creates an intimidating, hostile, or offensive environment.

## Leadership position

includes group Chairs, project maintainers, staff members, and Board members.

## Participant

includes the following persons:

    Developers
    Representatives of corporate participants
    Anyone from the Public partaking in the LF Projects work environment (e.g. contribute code, comment on our code or specs, email us, attend our conferences, functions, etc)

## Respect

is the genuine consideration you have for someone (if only because of their status as participant in LF Projects, like yourself), and that you show by treating them in a polite and kind way.

## Sexual harassment

includes visual displays of degrading sexual images, sexually suggestive conduct, offensive remarks of a sexual nature, requests for sexual favors, unwelcome physical contact, and sexual assault.

## Unwelcome behavior

Hard to define? Some questions to ask yourself are:

    how would I feel if I were in the position of the recipient?
    would my spouse, parent, child, sibling or friend like to be treated this way?
    would I like an account of my behavior published in the organization’s newsletter?
    could my behavior offend or hurt other members of the work group?
    could someone misinterpret my behavior as intentionally harmful or harassing?
    would I treat my boss or a person I admire at work like that ?

Summary: if you are unsure whether something might be welcome or unwelcome, don’t do it.

## Unwelcome sexual advance

includes requests for sexual favors, and other verbal or physical conduct of a sexual nature, where:

    submission to such conduct is made either explicitly or implicitly a term or condition of an individual’s employment,
    submission to or rejection of such conduct by an individual is used as a basis for employment decisions affecting the individual,
    such conduct has the purpose or effect of unreasonably interfering with an individual’s work performance or creating an intimidating hostile or offensive working environment.

## Workplace Bullying

is a tendency of individuals or groups to use persistent aggressive or unreasonable behavior (e.g. verbal or written abuse, offensive conduct or any interference which undermines or impedes work) against a co-worker or any professional relations.

## Work Environment

is the set of all available means of collaboration, including, but not limited to messages to mailing lists, private correspondence, Web pages, chat channels, phone and video teleconferences, and any kind of face-to-face meetings or discussions.

## Incident Procedure

To report incidents or to appeal reports of incidents, send email to the Manager of LF Projects, Mike Dolan (<EMAIL>). Please include any available relevant information, including links to any publicly accessible material relating to the matter. Every effort will be taken to ensure a safe and collegial environment in which to collaborate on matters relating to the Project. In order to protect the community, the Project reserves the right to take appropriate action, potentially including the removal of an individual from any and all participation in the project. The Project will work towards an equitable resolution in the event of a misunderstanding.

## Credits

This code is based on the W3C’s Code of Ethics and Professional Conduct with some additions from the Cloud Foundry’s Code of Conduct and the Hyperledger Project Code of Conduct.

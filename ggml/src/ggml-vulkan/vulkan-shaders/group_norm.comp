#version 450

#include "generic_head.comp"
#include "types.comp"

#extension GL_EXT_control_flow_attributes : enable
#define BLOCK_SIZE 512

layout(local_size_x = BLOCK_SIZE, local_size_y = 1, local_size_z = 1) in;

layout (binding = 0) readonly buffer X {A_TYPE data_a[];};
layout (binding = 1) writeonly buffer D {D_TYPE data_d[];};

shared float tmp[BLOCK_SIZE];

void main() {
    const uint group_size = p.KX;
    const float eps = p.param1;

    const uint tid = gl_LocalInvocationID.x;
    const uint start = gl_WorkGroupID.x * group_size + tid;
    const uint end = (gl_WorkGroupID.x + 1) * group_size;

    tmp[tid] = 0.0f;

    // Calculate mean
    [[unroll]] for (uint col = start; col < end; col += BLOCK_SIZE) {
        tmp[tid] += float(data_a[col]);
    }

    // tmp up partial tmps and write back result
    barrier();
    [[unroll]] for (int s = BLOCK_SIZE / 2; s > 0; s >>= 1) {
        if (tid < s) {
            tmp[tid] += tmp[tid + s];
        }
        barrier();
    }

    const float mean = tmp[0] / group_size;
    barrier();
    tmp[tid] = 0.0f;

    // Calculate variance
    [[unroll]] for (uint col = start; col < end; col += BLOCK_SIZE) {
        const float xi = float(data_a[col]) - mean;
        data_d[col] = D_TYPE(xi);
        tmp[tid] += xi * xi;
    }

    // sum up partial sums and write back result
    barrier();
    [[unroll]] for (int s = BLOCK_SIZE / 2; s > 0; s >>= 1) {
        if (tid < s) {
            tmp[tid] += tmp[tid + s];
        }
        barrier();
    }

    const float variance = tmp[0] / group_size;
    const float scale = inversesqrt(variance + eps);

    [[unroll]] for (uint col = start; col < end; col += BLOCK_SIZE) {
        data_d[col] *= D_TYPE(scale);
    }
}

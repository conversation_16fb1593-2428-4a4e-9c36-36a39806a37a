#version 450

#include "types.comp"
#include "generic_binary_head.comp"

layout(local_size_x = 512, local_size_y = 1, local_size_z = 1) in;

void main() {
    const uint idx = gl_GlobalInvocationID.x;
    if (idx >= p.ne) {
        return;
    }

    const uint offset = p.param3;
    const uint src1_i = idx - offset;
    const uint oz = src1_i / p.nb02;
    const uint oy = (src1_i - (oz * p.nb02)) / p.nb01;
    const uint ox = src1_i % p.nb01;

    uint i00, i01, i02, i03;
    get_indices(idx, i00, i01, i02, i03);

    if (ox < p.ne10 && oy < p.ne11 && oz < p.ne12) {
        data_d[get_doffset() + dst_idx(i00, i01, i02, i03)] = D_TYPE(FLOAT_TYPE(data_a[get_aoffset() + src0_idx(i00, i01, i02, i03)]) + FLOAT_TYPE(data_b[get_boffset() + ox + oy * p.ne10 + oz * p.ne10 * p.ne11]));
    } else {
        data_d[get_doffset() + dst_idx(i00, i01, i02, i03)] = D_TYPE(FLOAT_TYPE(data_a[get_aoffset() + src0_idx(i00, i01, i02, i03)]));
    }
}


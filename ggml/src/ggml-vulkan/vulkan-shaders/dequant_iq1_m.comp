#version 450

#extension GL_EXT_shader_explicit_arithmetic_types_int16 : require

#include "dequant_head.comp"

layout(local_size_x = 256, local_size_y = 1, local_size_z = 1) in;

layout (binding = 0) readonly buffer A {block_iq1_m data_a[];};
layout (binding = 1) writeonly buffer D {D_TYPE data_b[];};

void main() {
    // Each thread handles 1 subblock (32 values with 2 scales)
    const uint ib = gl_WorkGroupID.x * 32 + gl_LocalInvocationID.x / 8;

    init_iq_shmem(gl_WorkGroupSize);

    if (ib >= p.nel / 256) {
        return;
    }

    const uint ib32 = gl_LocalInvocationID.x % 8;
    const uint ib64 = ib32 / 2;
    const uint b_idx = 256 * ib + 32 * ib32;

    const uint16_t[4] scales = data_a[ib].scales;
    const u16vec4 s = u16vec4(scales[0], scales[1], scales[2], scales[3]) >> 12;
    const float d = float(unpackHalf2x16(s.x | (s.y << 4) | (s.z << 8) | (s.w << 12)).x);

    const uint sc = data_a[ib].scales[ib64];
    [[unroll]] for (int l = 0; l < 4; ++l) {
        const uint ib16 = 2 * ib32 + l / 2;
        const float dl = d * (2 * bitfieldExtract(sc, 3 * int(ib16 & 3), 3) + 1);
        const uint qh = data_a[ib].qh[ib16] >> (4 * (l & 1));
        const uint qs = data_a[ib].qs[4 * ib32 + l];
        const float delta = ((qh & 8) != 0) ? -IQ1M_DELTA : IQ1M_DELTA;
        const int16_t grid = int16_t(iq1s_grid[qs | ((qh & 7) << 8)]);
        [[unroll]] for (int j = 0; j < 8; ++j) {
            data_b[b_idx + 8 * l + j] = D_TYPE(dl * (bitfieldExtract(grid, 2*j, 2) + delta));
        }
    }
}

#version 450

#include "dequant_head.comp"

layout(local_size_x = 256, local_size_y = 1, local_size_z = 1) in;

layout (binding = 0) readonly buffer A {block_iq2_xs data_a[];};
layout (binding = 1) writeonly buffer D {D_TYPE data_b[];};

void main() {
    // Each thread handles 1 subblock (32 values with 2 scales)
    const uint ib = gl_WorkGroupID.x * 32 + gl_LocalInvocationID.x / 8;

    init_iq_shmem(gl_WorkGroupSize);

    if (ib >= p.nel / 256) {
        return;
    }

    const uint ib32 = gl_LocalInvocationID.x % 8;
    const uint b_idx = 256 * ib + 32 * ib32;

    const float d = float(data_a[ib].d);
    const vec2 scale = vec2(data_a[ib].scales[ib32] & 0xf, data_a[ib].scales[ib32] >> 4);
    const vec2 db = d * (0.5 + scale) * 0.25;

    [[unroll]] for (uint l = 0; l < 4; ++l) {
        uint16_t qs = data_a[ib].qs[4 * ib32 + l];
        const uint sign7 = qs >> 9;
        const uint sign8 = sign7 | (bitCount(sign7) << 7); // parity bit
        const uvec2 grid = iq2xs_grid[qs & 511];
        const u8vec4 grid0 = unpack8(grid.x);
        const u8vec4 grid1 = unpack8(grid.y);
        data_b[b_idx + 8 * l + 0] = D_TYPE(db[l/2] * grid0.x * ((sign8 & 1) != 0 ? -1.0 : 1.0));
        data_b[b_idx + 8 * l + 1] = D_TYPE(db[l/2] * grid0.y * ((sign8 & 2) != 0 ? -1.0 : 1.0));
        data_b[b_idx + 8 * l + 2] = D_TYPE(db[l/2] * grid0.z * ((sign8 & 4) != 0 ? -1.0 : 1.0));
        data_b[b_idx + 8 * l + 3] = D_TYPE(db[l/2] * grid0.w * ((sign8 & 8) != 0 ? -1.0 : 1.0));
        data_b[b_idx + 8 * l + 4] = D_TYPE(db[l/2] * grid1.x * ((sign8 & 16) != 0 ? -1.0 : 1.0));
        data_b[b_idx + 8 * l + 5] = D_TYPE(db[l/2] * grid1.y * ((sign8 & 32) != 0 ? -1.0 : 1.0));
        data_b[b_idx + 8 * l + 6] = D_TYPE(db[l/2] * grid1.z * ((sign8 & 64) != 0 ? -1.0 : 1.0));
        data_b[b_idx + 8 * l + 7] = D_TYPE(db[l/2] * grid1.w * ((sign8 & 128) != 0 ? -1.0 : 1.0));
    }
}
